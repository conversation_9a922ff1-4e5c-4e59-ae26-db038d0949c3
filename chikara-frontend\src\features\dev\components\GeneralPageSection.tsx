import Button from "@/components/Buttons/Button";
import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import { handleLogout } from "@/helpers/handleLogout";
import { handlePost } from "@/helpers/axiosInstance";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../../app/store/stores";
import toast from "react-hot-toast";

const GeneralPageSection = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();
    const setAuthed = useAuthStore((state) => state.setAuthed);

    // Mutation hooks for dev operations
    const addXpMutation = useMutation(
        orpc.dev.addXp.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () =>
                        queryClient.invalidateQueries({
                            queryKey: APIROUTES.USER.CURRENTUSERINFO,
                        }),
                    100
                );
                toast.success("XP added successfully!");
            },
            onError: (error) => {
                console.error("Error adding XP:", error);
                toast.error(error.message || "Failed to add XP");
            },
        })
    );

    const addCashMutation = useMutation(
        orpc.dev.addCash.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () =>
                        queryClient.invalidateQueries({
                            queryKey: APIROUTES.USER.CURRENTUSERINFO,
                        }),
                    100
                );
                toast.success("Cash added successfully!");
            },
            onError: (error) => {
                console.error("Error adding cash:", error);
                toast.error(error.message || "Failed to add cash");
            },
        })
    );

    const addStatsMutation = useMutation(
        orpc.dev.addStats.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () =>
                        queryClient.invalidateQueries({
                            queryKey: APIROUTES.USER.CURRENTUSERINFO,
                        }),
                    100
                );
                toast.success("Stats added successfully!");
            },
            onError: (error) => {
                console.error("Error adding stats:", error);
                toast.error(error.message || "Failed to add stats");
            },
        })
    );

    const removeStatsMutation = useMutation(
        orpc.dev.removeStats.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () =>
                        queryClient.invalidateQueries({
                            queryKey: APIROUTES.USER.CURRENTUSERINFO,
                        }),
                    100
                );
                toast.success("Stats removed successfully!");
            },
            onError: (error) => {
                console.error("Error removing stats:", error);
                toast.error(error.message || "Failed to remove stats");
            },
        })
    );

    const resetQuestsMutation = useMutation(
        orpc.dev.resetQuests.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () => queryClient.invalidateQueries({ queryKey: api.quests.getCombinedQuestList.key() }),
                    100
                );
                setTimeout(() => queryClient.invalidateQueries({ queryKey: api.quests.availableQuests.key() }), 100);
                toast.success("Quests reset successfully!");
            },
            onError: (error) => {
                console.error("Error resetting quests:", error);
                toast.error(error.message || "Failed to reset quests");
            },
        })
    );

    // Handler functions
    const addXP = () => {
        addXpMutation.mutate({ xp: 2000 });
    };

    const addCash = () => {
        addCashMutation.mutate({});
    };

    const addStats = () => {
        addStatsMutation.mutate({});
    };

    const removeStats = () => {
        removeStatsMutation.mutate({});
    };

    const resetQuests = () => {
        resetQuestsMutation.mutate();
    };

    const completeQuestsMutation = useMutation(
        orpc.dev.completeQuests.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () => queryClient.invalidateQueries({ queryKey: api.quests.getCombinedQuestList.key() }),
                    100
                );
                setTimeout(() => queryClient.invalidateQueries({ queryKey: api.quests.availableQuests.key() }), 100);
                toast.success("All quests completed successfully!");
            },
            onError: (error) => {
                console.error("Error completing quests:", error);
                toast.error(error.message || "Failed to complete quests");
            },
        })
    );

    const completeQuests = () => {
        completeQuestsMutation.mutate();
    };

    const createTestUserMutation = useMutation(
        orpc.admin.createTestUser.mutationOptions({
            onSuccess: async (response) => {
                await handleLogout();
                setTimeout(() => {
                    login(response.email);
                }, 500);
                toast.success("Test user created successfully!");
            },
            onError: (error) => {
                console.error("Error creating test user:", error);
                toast.error(error.message || "Failed to create test user");
            },
        })
    );

    const randomRoguelikeMutation = useMutation(
        orpc.dev.randomRoguelike.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                setTimeout(() => queryClient.invalidateQueries({ queryKey: APIROUTES.ROGUELIKE.CURRENTMAP }), 100);
                navigate("/streets");
                toast.success("Random map generated successfully!");
            },
            onError: (error) => {
                console.error("Error generating random map:", error);
                toast.error(error.message || "Failed to generate random map");
            },
        })
    );

    const addAllItemsMutation = useMutation(
        orpc.dev.addAllItems.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
                toast.success("All items added successfully!");
            },
            onError: (error) => {
                console.error("Error adding all items:", error);
                toast.error(error.message || "Failed to add all items");
            },
        })
    );

    const login = async (email) => {
        try {
            const response = await handlePost(APIROUTES.AUTH.LOGIN, {
                email: email,
                password: "testtest123",
            });

            setAuthed(true);
            navigate("/home");
        } catch (error) {
            console.error("Login error:", error);
        }
    };

    const createTestUser = () => {
        createTestUserMutation.mutate();
    };

    const randomMap = () => {
        randomRoguelikeMutation.mutate();
    };

    const allItems = () => {
        addAllItemsMutation.mutate();
    };

    const deleteExploreNodesMutation = useMutation(
        orpc.dev.deleteExploreNodes.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.explore.map.key() });
                toast.success("Explore nodes reset successfully!");
            },
            onError: (error) => {
                console.error("Error resetting explore nodes:", error);
                toast.error(error.message || "Failed to reset explore nodes");
            },
        })
    );

    const resetExploreNodes = () => {
        deleteExploreNodesMutation.mutate();
    };

    return (
        <div className="grid grid-cols-2 gap-3 p-2">
            <Button className="text-sm!" type="primary" onClick={addXP}>
                Gain 2000 EXP
            </Button>
            <Button className="text-sm!" type="primary" onClick={addCash}>
                +5k Cash
            </Button>
            <Button className="text-sm!" type="primary" onClick={addStats}>
                +200 Stats
            </Button>
            <Button className="text-sm!" type="primary" onClick={removeStats}>
                -200 Stats
            </Button>
            <Button className="text-sm!" type="primary" onClick={resetQuests}>
                Reset Quests
            </Button>
            <Button className="text-sm!" type="primary" onClick={randomMap}>
                Random Map
            </Button>
            <Button className="text-sm!" type="primary" onClick={allItems}>
                Add All Items
            </Button>
            <Button className="text-sm!" type="primary" onClick={completeQuests}>
                Complete Quests
            </Button>
            <Button className="text-sm!" type="primary" onClick={createTestUser}>
                Create Test User
            </Button>
            <Button className="text-sm!" type="primary" onClick={resetExploreNodes}>
                Reset Explore Nodes
            </Button>
        </div>
    );
};

export default GeneralPageSection;
