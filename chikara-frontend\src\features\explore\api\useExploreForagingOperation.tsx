import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";

const useExploreForagingOperation = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.explore.processForagingOperation.mutationOptions({
            onSuccess: () => {
                // Only invalidate inventory and user info, not the explore map
                // The explore map will be invalidated when the node is completed via useCompleteNode
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
            },
        })
    );
};

export default useExploreForagingOperation;
