import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

export const useStartMission = (options = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.missions.start.mutationOptions({
            onSuccess: () => {
                // Invalidate user info to update current mission status
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });

                // Invalidate mission queries
                queryClient.invalidateQueries({
                    queryKey: api.missions.getList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.missions.getCurrent.key(),
                });

                toast.success("Mission Started!");
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
            ...options,
        })
    );
};
