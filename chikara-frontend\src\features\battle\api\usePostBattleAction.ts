import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const usePostBattleAction = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.battle.postBattleAction.mutationOptions({
            onSuccess: () => {
                // Invalidate battle status and user info to refresh UI
                queryClient.invalidateQueries({ queryKey: api.battle.getStatus.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
            },
        })
    );
};

export default usePostBattleAction;
