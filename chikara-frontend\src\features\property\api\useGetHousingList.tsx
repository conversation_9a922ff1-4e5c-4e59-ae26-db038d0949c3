import { api } from "@/helpers/api";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { useQuery } from "@tanstack/react-query";

/**
 * Hook to get list of available housing properties
 */
export const useGetHousingList = (options: QueryOptions = {}) => {
    return useQuery(
        api.property.getHousingList.queryOptions({
            staleTime: 300000, // 5 minutes
            ...options,
        })
    );
};
