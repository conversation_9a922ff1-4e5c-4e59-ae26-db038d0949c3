import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useExploreScavengeChoice = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.explore.makeScavengeChoice.mutationOptions({
            onSuccess: (data, variables, context) => {
                // Only invalidate inventory and user info, not the explore map
                // The explore map will be invalidated when the node is completed via useCompleteNode
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
            },
        })
    );
};

export default useExploreScavengeChoice;
