import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Request parameters for canceling a craft
 */
export interface CancelCraftParams {
    id: number;
}

/**
 * Custom hook to cancel an in-progress crafting operation using ORPC
 * @param onSuccessCallback - Optional callback function to execute on successful cancellation
 */
const useCancelCraft = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.crafting.cancelCraft.mutationOptions({
            onSuccess: () => {
                toast.success("Crafting cancelled!");

                // Invalidate relevant queries to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.crafting.getCraftingQueue.key(),
                });

                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.INVENTORY,
                });

                // Execute additional callback if provided
                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error("Cancel craft error:", errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};

export default useCancelCraft;
