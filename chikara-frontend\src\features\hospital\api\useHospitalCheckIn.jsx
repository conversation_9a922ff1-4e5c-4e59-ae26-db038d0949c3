import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useHospitalCheckIn = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.infirmary.hospitalCheckIn.mutationOptions({
            onSuccess: () => {
                toast.success(`Checked in successfully!`);
                queryClient.invalidateQueries({
                    queryKey: api.infirmary.getHospitalList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    return {
        hospitalCheckIn: mutation,
    };
};

export default useHospitalCheckIn;
